import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import {
  faPlus,
  faMinus,
  faEdit,
  faSave,
  faArrowLeft,
  faCalendarAlt,
  faArrowRight,
  faCheck,
  faInfoCircle,
  faClock,
  faDumbbell,
  faEye
} from '@fortawesome/free-solid-svg-icons';

import { WorkoutProgramService } from '../../services/workout-program.service';
import { WorkoutProgramDayModalComponent } from './workout-program-day-modal.component';
import {
  WorkoutProgramTemplateAdd,
  WorkoutProgramDayAdd,
  POPULAR_DAY_NAMES,
  EXPERIENCE_LEVELS,
  TARGET_GOALS
} from '../../models/workout-program.models';

// Wizard adımları enum'u
enum WizardStep {
  BASIC_INFO = 1,
  DAY_PLANNING = 2,
  EXERCISE_SETUP = 3,
  PREVIEW = 4
}

@Component({
  selector: 'app-workout-program-add',
  templateUrl: './workout-program-add.component.html',
  styleUrls: ['./workout-program-add.component.css'],
  standalone: false
})
export class WorkoutProgramAddComponent implements OnInit {
  // Icons
  faPlus = faPlus;
  faMinus = faMinus;
  faEdit = faEdit;
  faSave = faSave;
  faArrowLeft = faArrowLeft;
  faCalendarAlt = faCalendarAlt;
  faArrowRight = faArrowRight;
  faCheck = faCheck;
  faInfoCircle = faInfoCircle;
  faClock = faClock;
  faDumbbell = faDumbbell;
  faEye = faEye;

  // Wizard state
  currentStep: WizardStep = WizardStep.BASIC_INFO;
  WizardStep = WizardStep; // Template'de kullanmak için

  // Forms
  basicInfoForm!: FormGroup;
  dayPlanningForm!: FormGroup;
  isSubmitting = false;

  // Program data
  programData: any = {};
  selectedDayCount: number = 7; // Sabit 7 gün

  // Basit ilerleme sistemi
  completedSteps: Set<WizardStep> = new Set();

  // Constants
  popularDayNames = POPULAR_DAY_NAMES;
  experienceLevels = EXPERIENCE_LEVELS;
  targetGoals = TARGET_GOALS;

  constructor(
    private fb: FormBuilder,
    private workoutProgramService: WorkoutProgramService,
    private router: Router,
    private toastrService: ToastrService,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initializeForms();
  }

  initializeForms(): void {
    // Adım 1: Temel bilgiler formu
    this.basicInfoForm = this.fb.group({
      programName: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]],
      experienceLevel: ['', [Validators.required]],
      targetGoal: ['', [Validators.required]]
    });

    // Adım 2: Gün planlama formu (7 günlük sabit)
    this.dayPlanningForm = this.fb.group({
      days: this.fb.array([])
    });

    // Günleri henüz oluşturma - sadece gün planlaması adımına geçildiğinde oluştur
  }

  get days(): FormArray {
    return this.dayPlanningForm.get('days') as FormArray;
  }

  // Wizard navigation methods
  nextStep(): void {
    if (this.canProceedToNextStep()) {
      // Mevcut adımı tamamlandı olarak işaretle
      this.completedSteps.add(this.currentStep);

      if (this.currentStep < WizardStep.PREVIEW) {
        this.currentStep++;
        this.onStepChange();
      }
    } else {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.validateAndHighlightErrors();
    }
  }

  previousStep(): void {
    if (this.currentStep > WizardStep.BASIC_INFO) {
      this.currentStep--;
    }
  }

  goToStep(step: WizardStep): void {
    // Basit kontrol: sadece tamamlanmış adımlara veya bir sonraki adıma gidebilir
    if (this.canGoToStep(step)) {
      this.currentStep = step;
      this.onStepChange();
    }
  }

  // Bir adıma gidilip gidilemeyeceğini kontrol et
  canGoToStep(step: WizardStep): boolean {
    // Mevcut adıma her zaman gidebilir
    if (step === this.currentStep) return true;

    // Tamamlanmış adımlara gidebilir
    if (this.completedSteps.has(step)) return true;

    // Bir sonraki adıma gidebilir mi kontrol et
    const previousStep = step - 1;
    if (previousStep >= WizardStep.BASIC_INFO) {
      return this.completedSteps.has(previousStep);
    }

    // İlk adıma her zaman gidebilir
    return step === WizardStep.BASIC_INFO;
  }

  onStepChange(): void {
    switch (this.currentStep) {
      case WizardStep.DAY_PLANNING:
        this.saveBasicInfo();
        // Gün planlaması adımına ilk kez geçildiğinde günleri oluştur
        if (this.days.length === 0) {
          this.initializeDayPlanningForm();
        }
        break;
      case WizardStep.EXERCISE_SETUP:
        this.saveDayPlanningInfo();
        break;
      case WizardStep.PREVIEW:
        this.preparePreviewData();
        break;
    }
  }

  // Step validation methods
  canProceedToNextStep(): boolean {
    switch (this.currentStep) {
      case WizardStep.BASIC_INFO:
        return this.basicInfoForm.valid;
      case WizardStep.DAY_PLANNING:
        return this.dayPlanningForm.valid;
      case WizardStep.EXERCISE_SETUP:
        return this.areAllDaysConfigured();
      default:
        return true;
    }
  }

  // Basit tamamlanma kontrolü
  isStepCompleted(step: WizardStep): boolean {
    return this.completedSteps.has(step);
  }

  // UI Helper metodları
  getStepButtonClass(step: WizardStep): string {
    if (this.currentStep === step) {
      return 'btn-primary'; // Aktif adım - mavi
    } else if (this.isStepCompleted(step)) {
      return 'btn-outline-success'; // Tamamlanmış adım - yeşil çerçeve
    } else {
      return 'btn-outline-secondary'; // Henüz erişilmemiş adım - gri
    }
  }

  getStepIcon(step: WizardStep): any {
    // Tamamlanmış adımlar için check ikonu
    if (this.isStepCompleted(step)) {
      return this.faCheck;
    }

    // Aktif veya henüz tamamlanmamış adımlar için varsayılan ikon
    switch (step) {
      case WizardStep.BASIC_INFO:
        return this.faInfoCircle;
      case WizardStep.DAY_PLANNING:
        return this.faCalendarAlt;
      case WizardStep.EXERCISE_SETUP:
        return this.faDumbbell;
      case WizardStep.PREVIEW:
        return this.faEye;
      default:
        return this.faInfoCircle;
    }
  }

  areAllDaysConfigured(): boolean {
    return this.days.controls.every(dayControl => {
      const isRestDay = dayControl.get('isRestDay')?.value;
      if (isRestDay) return true;

      const exercises = dayControl.get('exercises')?.value || [];
      return exercises.length > 0;
    });
  }

  // Data saving methods
  saveBasicInfo(): void {
    if (this.basicInfoForm.valid) {
      this.programData = {
        ...this.programData,
        ...this.basicInfoForm.value
      };
      // 7 günlük program olarak ayarla
      this.programData.dayCount = 7;
    }
  }

  saveDayPlanningInfo(): void {
    if (this.dayPlanningForm.valid) {
      this.programData.days = this.days.value;
    }
  }

  preparePreviewData(): void {
    this.saveDayPlanningInfo();
    // Preview için tüm verileri hazırla
  }

  // Day planning methods
  initializeDayPlanningForm(): void {
    // Mevcut günleri temizle
    while (this.days.length !== 0) {
      this.days.removeAt(0);
    }

    // 7 günlük program için günleri oluştur
    const dayNames = ['Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar'];
    for (let i = 1; i <= 7; i++) {
      const dayGroup = this.createDayFormGroup(i);
      // Varsayılan gün adını ayarla
      dayGroup.get('dayName')?.setValue(dayNames[i - 1]);
      this.days.push(dayGroup);
    }
  }

  createDayFormGroup(dayNumber: number = 1): FormGroup {
    return this.fb.group({
      dayNumber: [dayNumber, [Validators.required, Validators.min(1), Validators.max(7)]],
      dayName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      isRestDay: [false],
      exercises: this.fb.array([])
    });
  }

  onPopularDayNameSelect(dayIndex: number, dayName: string): void {
    const dayControl = this.days.at(dayIndex);
    dayControl.get('dayName')?.setValue(dayName);

    // Eğer "Dinlenme Günü" seçildiyse, dinlenme günü switch'ini aktif et
    if (dayName === 'Dinlenme Günü') {
      dayControl.get('isRestDay')?.setValue(true);
      // Egzersizleri temizle
      const exercisesArray = dayControl.get('exercises') as FormArray;
      exercisesArray.clear();
    }
  }

  onRestDayChange(dayIndex: number): void {
    const dayControl = this.days.at(dayIndex);
    const isRestDay = dayControl.get('isRestDay')?.value;

    if (isRestDay) {
      // Dinlenme günü ise egzersizleri temizle
      const exercisesArray = dayControl.get('exercises') as FormArray;
      exercisesArray.clear();

      // Gün adını "Dinlenme Günü" olarak ayarla
      dayControl.get('dayName')?.setValue('Dinlenme Günü');
    } else {
      // Dinlenme günü kapatıldıysa ve gün adı "Dinlenme Günü" ise temizle
      const currentDayName = dayControl.get('dayName')?.value;
      if (currentDayName === 'Dinlenme Günü') {
        dayControl.get('dayName')?.setValue('');
      }
    }
  }

  // Exercise setup methods
  openDayEditModal(dayIndex: number): void {
    const dayControl = this.days.at(dayIndex);
    const dayData = dayControl.value;

    const dialogRef = this.dialog.open(WorkoutProgramDayModalComponent, {
      width: '1000px',
      maxWidth: '95vw',
      height: '80vh',
      maxHeight: '80vh',
      data: {
        day: dayData,
        dayNumber: dayData.dayNumber,
        mode: 'edit'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Sadece egzersizleri güncelle
        const exercisesArray = dayControl.get('exercises') as FormArray;
        exercisesArray.clear();

        if (result.exercises && result.exercises.length > 0) {
          result.exercises.forEach((exercise: any) => {
            const exerciseGroup = this.fb.group({
              exerciseType: [exercise.exerciseType],
              exerciseID: [exercise.exerciseID],
              exerciseName: [exercise.exerciseName],
              orderIndex: [exercise.orderIndex],
              sets: [exercise.sets],
              reps: [exercise.reps],
              notes: [exercise.notes]
            });
            exercisesArray.push(exerciseGroup);
          });
        }
      }
    });
  }

  getDayExerciseCount(dayIndex: number): number {
    const dayControl = this.days.at(dayIndex);
    const exercises = dayControl.get('exercises')?.value || [];
    return exercises.length;
  }

  isDayConfigured(dayIndex: number): boolean {
    const dayControl = this.days.at(dayIndex);
    const isRestDay = dayControl.get('isRestDay')?.value;
    if (isRestDay) return true;

    const exercises = dayControl.get('exercises')?.value || [];
    return exercises.length > 0;
  }

  // Final submission
  onSubmit(): void {
    if (this.canSubmitProgram()) {
      this.isSubmitting = true;

      // Tüm verileri birleştir
      const finalProgramData: WorkoutProgramTemplateAdd = {
        programName: this.programData.programName,
        description: this.programData.description,
        experienceLevel: this.programData.experienceLevel,
        targetGoal: this.programData.targetGoal,
        days: this.days.value.map((day: any) => ({
          dayNumber: day.dayNumber,
          dayName: day.dayName,
          isRestDay: day.isRestDay,
          exercises: day.exercises || []
        }))
      };

      this.workoutProgramService.add(finalProgramData).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Antrenman programı başarıyla oluşturuldu', 'Başarılı');
            this.router.navigate(['/workout-programs']);
          } else {
            this.toastrService.error(response.message || 'Program oluşturulurken hata oluştu', 'Hata');
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          console.error('Error creating workout program:', error);
          this.toastrService.error('Program oluşturulurken hata oluştu', 'Hata');
          this.isSubmitting = false;
        }
      });
    } else {
      this.toastrService.warning('Lütfen tüm adımları tamamlayın', 'Uyarı');
    }
  }

  canSubmitProgram(): boolean {
    return this.isStepCompleted(WizardStep.BASIC_INFO) &&
           this.isStepCompleted(WizardStep.DAY_PLANNING) &&
           this.isStepCompleted(WizardStep.EXERCISE_SETUP);
  }

  // Utility methods
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          }
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/workout-programs']);
  }

  // Form validation helpers
  isBasicInfoFieldInvalid(fieldName: string): boolean {
    const field = this.basicInfoForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isDayFieldInvalid(dayIndex: number, fieldName: string): boolean {
    const dayControl = this.days.at(dayIndex);
    const field = dayControl.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getBasicInfoFieldError(fieldName: string): string {
    const field = this.basicInfoForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['minlength']) return `En az ${field.errors['minlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['maxlength']) return `En fazla ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
    }
    return '';
  }



  getDayFieldError(dayIndex: number, fieldName: string): string {
    const dayControl = this.days.at(dayIndex);
    const field = dayControl.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['minlength']) return `En az ${field.errors['minlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['maxlength']) return `En fazla ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['min']) return `En az ${field.errors['min'].min} olmalıdır`;
      if (field.errors['max']) return `En fazla ${field.errors['max'].max} olmalıdır`;
    }
    return '';
  }

  // Progress calculation
  getProgressPercentage(): number {
    const totalSteps = 4;
    return (this.currentStep / totalSteps) * 100;
  }

  getStepTitle(): string {
    switch (this.currentStep) {
      case WizardStep.BASIC_INFO:
        return 'Program Bilgileri';
      case WizardStep.DAY_PLANNING:
        return 'Gün Planlaması (7 Günlük)';
      case WizardStep.EXERCISE_SETUP:
        return 'Egzersiz Kurulumu';
      case WizardStep.PREVIEW:
        return 'Önizleme ve Kaydet';
      default:
        return '';
    }
  }

  getStepDescription(): string {
    switch (this.currentStep) {
      case WizardStep.BASIC_INFO:
        return 'Program adı, açıklama, deneyim seviyesi ve hedef bilgilerini girin';
      case WizardStep.DAY_PLANNING:
        return '7 günlük programınızda her günü isimlendirin ve egzersiz/dinlenme günlerini belirleyin';
      case WizardStep.EXERCISE_SETUP:
        return 'Egzersiz günleri için egzersizleri ekleyin ve düzenleyin';
      case WizardStep.PREVIEW:
        return 'Programınızı gözden geçirin ve kaydedin';
      default:
        return '';
    }
  }

  // Preview helper methods
  getTrainingDayCount(): number {
    return this.days.controls.filter(dayControl => !dayControl.get('isRestDay')?.value).length;
  }

  getRestDayCount(): number {
    return this.days.controls.filter(dayControl => dayControl.get('isRestDay')?.value).length;
  }

  getTotalExerciseCount(): number {
    return this.days.controls.reduce((total, dayControl) => {
      const exercises = dayControl.get('exercises')?.value || [];
      return total + exercises.length;
    }, 0);
  }

  // Validation ve Error Highlighting Sistemi
  validateAndHighlightErrors(): void {
    const missingFields: string[] = [];
    const requiredControls: (HTMLElement | null)[] = [];

    switch (this.currentStep) {
      case WizardStep.BASIC_INFO:
        this.validateBasicInfoStep(missingFields, requiredControls);
        break;
      case WizardStep.DAY_PLANNING:
        this.validateDayPlanningStep(missingFields, requiredControls);
        break;
      case WizardStep.EXERCISE_SETUP:
        this.validateExerciseSetupStep(missingFields, requiredControls);
        break;
    }

    if (missingFields.length > 0) {
      this.highlightAndShakeFields(requiredControls);
    }
  }

  private validateBasicInfoStep(missingFields: string[], requiredControls: (HTMLElement | null)[]): void {
    // Tüm form kontrollerini dokunulmuş olarak işaretle
    this.markFormGroupTouched(this.basicInfoForm);

    // Program Adı kontrolü
    const programNameControl = this.basicInfoForm.get('programName');
    if (programNameControl?.invalid) {
      missingFields.push('Program Adı');
      requiredControls.push(document.getElementById('programName'));
    }

    // Deneyim Seviyesi kontrolü
    const experienceLevelControl = this.basicInfoForm.get('experienceLevel');
    if (experienceLevelControl?.invalid) {
      missingFields.push('Deneyim Seviyesi');
      requiredControls.push(document.getElementById('experienceLevel'));
    }

    // Hedef kontrolü
    const targetGoalControl = this.basicInfoForm.get('targetGoal');
    if (targetGoalControl?.invalid) {
      missingFields.push('Hedef');
      requiredControls.push(document.getElementById('targetGoal'));
    }
  }

  private validateDayPlanningStep(missingFields: string[], requiredControls: (HTMLElement | null)[]): void {
    // Tüm gün kontrollerini dokunulmuş olarak işaretle
    this.markFormGroupTouched(this.dayPlanningForm);

    this.days.controls.forEach((dayControl, index) => {
      const dayNameControl = dayControl.get('dayName');
      if (dayNameControl?.invalid) {
        missingFields.push(`${index + 1}. Gün Adı`);
        const element = document.getElementById(`dayName_${index}`);
        console.log(`Looking for dayName_${index}:`, element); // Debug için
        requiredControls.push(element);
      }
    });
  }

  private validateExerciseSetupStep(missingFields: string[], requiredControls: (HTMLElement | null)[]): void {
    this.days.controls.forEach((dayControl, index) => {
      const isRestDay = dayControl.get('isRestDay')?.value;
      if (!isRestDay) {
        const exercises = dayControl.get('exercises')?.value || [];
        if (exercises.length === 0) {
          missingFields.push(`${index + 1}. Gün Egzersizleri`);
          // Egzersiz ekleme butonunu hedefle
          requiredControls.push(document.getElementById(`exerciseBtn_${index}`));
        }
      }
    });
  }

  private highlightAndShakeFields(requiredControls: (HTMLElement | null)[]): void {
    console.log('highlightAndShakeFields called with:', requiredControls.length, 'controls');

    if (requiredControls.length > 0) {
      // Eksik alanları görsel olarak vurgula ve titret
      setTimeout(() => {
        requiredControls.forEach((element, index) => {
          console.log(`Processing element ${index}:`, element);
          if (element) {
            console.log(`Adding shake animation to:`, element);
            // Titreşim animasyonu ekle
            element.classList.add('shake-animation');

            // İlk eksik alana kaydır
            if (index === 0) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });

              // Input elementse focus yap
              if (element.tagName === 'INPUT' || element.tagName === 'SELECT' || element.tagName === 'TEXTAREA') {
                (element as HTMLInputElement).focus();
              }
            }

            // Animasyonu bir süre sonra kaldır
            setTimeout(() => {
              console.log(`Removing shake animation from:`, element);
              element.classList.remove('shake-animation');
            }, 600);
          } else {
            console.log(`Element ${index} is null`);
          }
        });
      }, 100);
    } else {
      console.log('No controls to shake');
    }
  }
}
