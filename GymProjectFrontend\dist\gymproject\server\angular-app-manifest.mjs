
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41452, hash: 'a2b9f1103389181c07ea51aef71d101786d2329d0864b614df64d9f939c7cadc', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: 'fcad1be98083153a35a957c2cb397aa9ed534cb2917ea36c9c9ffd8e298a576d', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-MC4B3AGM.css': {size: 294869, hash: '1A4AQVWAvyE', text: () => import('./assets-chunks/styles-MC4B3AGM_css.mjs').then(m => m.default)}
  },
};
