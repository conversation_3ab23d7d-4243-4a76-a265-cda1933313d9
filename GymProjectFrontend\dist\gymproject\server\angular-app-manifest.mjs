
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41452, hash: '1d335f1450846dfd13b7cad37e59285a3e0e949e677d7ea59dc7fb377fd2824e', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '161bfcecfd1080babbc69d6109250bf44437e39412096e38308c6d26ce671f13', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-MC4B3AGM.css': {size: 294869, hash: '1A4AQVWAvyE', text: () => import('./assets-chunks/styles-MC4B3AGM_css.mjs').then(m => m.default)}
  },
};
