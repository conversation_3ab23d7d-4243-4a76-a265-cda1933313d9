
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41452, hash: '06b8babc4b3131a8abb2de730d420d7277199a3528b055a7a86b2aac03445391', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '65babd2389af6e1f2520bc7db237f38596833281ed63b130df96bc1620adcf6a', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-MC4B3AGM.css': {size: 294869, hash: '1A4AQVWAvyE', text: () => import('./assets-chunks/styles-MC4B3AGM_css.mjs').then(m => m.default)}
  },
};
