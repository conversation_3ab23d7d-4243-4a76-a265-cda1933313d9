<div class="container-fluid">
  <!-- Header -->
  <div class="modern-card mb-4">
    <div class="modern-card-header">
      <div class="d-flex align-items-center">
        <button
          class="modern-btn modern-btn-outline-secondary me-3"
          (click)="goBack()">
          <fa-icon [icon]="faArrowLeft"></fa-icon>
        </button>
        <div>
          <h4 class="mb-0"><PERSON><PERSON></h4>
          <small class="text-muted">{{getStepTitle()}} - {{getStepDescription()}}</small>
        </div>
      </div>
      <div class="d-flex gap-2">
        <button
          type="button"
          class="modern-btn modern-btn-secondary"
          (click)="goBack()">
          İptal
        </button>
        <button
          *ngIf="currentStep < WizardStep.PREVIEW"
          type="button"
          class="modern-btn modern-btn-outline-primary"
          [disabled]="currentStep === WizardStep.BASIC_INFO"
          (click)="previousStep()">
          <fa-icon [icon]="faArrowLeft" class="modern-btn-icon"></fa-icon>
          Önceki
        </button>
        <button
          *ngIf="currentStep < WizardStep.PREVIEW"
          type="button"
          class="modern-btn modern-btn-primary"
          [disabled]="!canProceedToNextStep()"
          (click)="nextStep()">
          Sonraki
          <fa-icon [icon]="faArrowRight" class="modern-btn-icon"></fa-icon>
        </button>
        <button
          *ngIf="currentStep === WizardStep.PREVIEW"
          type="button"
          class="modern-btn modern-btn-success"
          [disabled]="isSubmitting || !canSubmitProgram()"
          (click)="onSubmit()">
          <fa-icon [icon]="faSave" class="modern-btn-icon"></fa-icon>
          <span *ngIf="!isSubmitting">Programı Kaydet</span>
          <span *ngIf="isSubmitting">Kaydediliyor...</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Progress Bar -->
  <div class="modern-card mb-4">
    <div class="modern-card-body py-3">
      <div class="d-flex justify-content-between align-items-center mb-2">
        <span class="fw-bold">İlerleme</span>
        <span class="text-muted">{{currentStep}}/5</span>
      </div>
      <div class="progress" style="height: 8px;">
        <div
          class="progress-bar bg-primary"
          role="progressbar"
          [style.width.%]="getProgressPercentage()"
          [attr.aria-valuenow]="getProgressPercentage()"
          aria-valuemin="0"
          aria-valuemax="100">
        </div>
      </div>
    </div>
  </div>

  <!-- Step Navigation -->
  <div class="modern-card mb-4">
    <div class="modern-card-body py-3">
      <div class="row g-2">
        <div class="col">
          <button
            type="button"
            class="btn w-100 text-start"
            [class]="currentStep === WizardStep.BASIC_INFO ? 'btn-primary' : (isStepCompleted(WizardStep.BASIC_INFO) ? 'btn-outline-success' : 'btn-outline-secondary')"
            [disabled]="currentStep < WizardStep.BASIC_INFO"
            (click)="goToStep(WizardStep.BASIC_INFO)">
            <div class="d-flex align-items-center">
              <div class="me-2">
                <fa-icon [icon]="currentStep === WizardStep.BASIC_INFO ? faInfoCircle : (isStepCompleted(WizardStep.BASIC_INFO) ? faCheck : faInfoCircle)"></fa-icon>
              </div>
              <div>
                <div class="fw-bold">1. Program Bilgileri</div>
                <small class="text-muted">Temel bilgiler</small>
              </div>
            </div>
          </button>
        </div>
        <div class="col">
          <button
            type="button"
            class="btn w-100 text-start"
            [class]="currentStep === WizardStep.DAY_PLANNING ? 'btn-primary' : (isStepCompleted(WizardStep.DAY_PLANNING) ? 'btn-outline-success' : 'btn-outline-secondary')"
            [disabled]="!isStepCompleted(WizardStep.BASIC_INFO) && currentStep < WizardStep.DAY_PLANNING"
            (click)="goToStep(WizardStep.DAY_PLANNING)">
            <div class="d-flex align-items-center">
              <div class="me-2">
                <fa-icon [icon]="currentStep === WizardStep.DAY_PLANNING ? faCalendarAlt : (isStepCompleted(WizardStep.DAY_PLANNING) ? faCheck : faCalendarAlt)"></fa-icon>
              </div>
              <div>
                <div class="fw-bold">2. Gün Planlaması</div>
                <small class="text-muted">7 günlük plan</small>
              </div>
            </div>
          </button>
        </div>
        <div class="col">
          <button
            type="button"
            class="btn w-100 text-start"
            [class]="currentStep === WizardStep.EXERCISE_SETUP ? 'btn-primary' : (isStepCompleted(WizardStep.EXERCISE_SETUP) ? 'btn-outline-success' : 'btn-outline-secondary')"
            [disabled]="!isStepCompleted(WizardStep.DAY_PLANNING) && currentStep < WizardStep.EXERCISE_SETUP"
            (click)="goToStep(WizardStep.EXERCISE_SETUP)">
            <div class="d-flex align-items-center">
              <div class="me-2">
                <fa-icon [icon]="currentStep === WizardStep.EXERCISE_SETUP ? faDumbbell : (isStepCompleted(WizardStep.EXERCISE_SETUP) ? faCheck : faDumbbell)"></fa-icon>
              </div>
              <div>
                <div class="fw-bold">3. Egzersiz Kurulumu</div>
                <small class="text-muted">Egzersizleri ekle</small>
              </div>
            </div>
          </button>
        </div>
        <div class="col">
          <button
            type="button"
            class="btn w-100 text-start"
            [class]="currentStep === WizardStep.PREVIEW ? 'btn-primary' : (isStepCompleted(WizardStep.EXERCISE_SETUP) ? 'btn-outline-success' : 'btn-outline-secondary')"
            [disabled]="!isStepCompleted(WizardStep.EXERCISE_SETUP) && currentStep < WizardStep.PREVIEW"
            (click)="goToStep(WizardStep.PREVIEW)">
            <div class="d-flex align-items-center">
              <div class="me-2">
                <fa-icon [icon]="currentStep === WizardStep.PREVIEW ? faEye : (isStepCompleted(WizardStep.EXERCISE_SETUP) ? faCheck : faEye)"></fa-icon>
              </div>
              <div>
                <div class="fw-bold">4. Önizleme</div>
                <small class="text-muted">Kaydet</small>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Step 1: Basic Information -->
  <div *ngIf="currentStep === WizardStep.BASIC_INFO" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>
        Program Temel Bilgileri
      </h5>
    </div>
    <div class="modern-card-body">
      <form [formGroup]="basicInfoForm">
        <div class="row g-3">
          <!-- Program Name -->
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">
                Program Adı <span class="text-danger">*</span>
              </label>
              <input
                type="text"
                class="modern-form-control"
                [class.is-invalid]="isBasicInfoFieldInvalid('programName')"
                formControlName="programName"
                placeholder="Örn: Başlangıç Kas Yapma Programı">
              <div *ngIf="isBasicInfoFieldInvalid('programName')" class="invalid-feedback">
                {{getBasicInfoFieldError('programName')}}
              </div>
            </div>
          </div>

          <!-- Experience Level -->
          <div class="col-md-3">
            <div class="modern-form-group">
              <label class="modern-form-label">
                Deneyim Seviyesi <span class="text-danger">*</span>
              </label>
              <select
                class="modern-form-control"
                [class.is-invalid]="isBasicInfoFieldInvalid('experienceLevel')"
                formControlName="experienceLevel">
                <option value="">Seçiniz</option>
                <option *ngFor="let level of experienceLevels" [value]="level.value">
                  {{level.label}}
                </option>
              </select>
              <div *ngIf="isBasicInfoFieldInvalid('experienceLevel')" class="invalid-feedback">
                {{getBasicInfoFieldError('experienceLevel')}}
              </div>
            </div>
          </div>

          <!-- Target Goal -->
          <div class="col-md-3">
            <div class="modern-form-group">
              <label class="modern-form-label">
                Hedef <span class="text-danger">*</span>
              </label>
              <select
                class="modern-form-control"
                [class.is-invalid]="isBasicInfoFieldInvalid('targetGoal')"
                formControlName="targetGoal">
                <option value="">Seçiniz</option>
                <option *ngFor="let goal of targetGoals" [value]="goal.value">
                  {{goal.label}}
                </option>
              </select>
              <div *ngIf="isBasicInfoFieldInvalid('targetGoal')" class="invalid-feedback">
                {{getBasicInfoFieldError('targetGoal')}}
              </div>
            </div>
          </div>

          <!-- Description -->
          <div class="col-12">
            <div class="modern-form-group">
              <label class="modern-form-label">Açıklama (Opsiyonel)</label>
              <textarea
                class="modern-form-control"
                [class.is-invalid]="isBasicInfoFieldInvalid('description')"
                formControlName="description"
                rows="3"
                placeholder="Program hakkında detaylı bilgi..."></textarea>
              <div *ngIf="isBasicInfoFieldInvalid('description')" class="invalid-feedback">
                {{getBasicInfoFieldError('description')}}
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Step 2: Day Planning -->
  <div *ngIf="currentStep === WizardStep.DAY_PLANNING" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faCalendarAlt" class="me-2"></fa-icon>
        7 Günlük Program Planlaması
      </h5>
      <p class="mb-0 mt-2 text-muted">
        <small>Her günü isimlendirin ve egzersiz/dinlenme günü olarak ayarlayın</small>
      </p>
    </div>
    <div class="modern-card-body">
      <form [formGroup]="dayPlanningForm">
        <div formArrayName="days">
          <div
            *ngFor="let dayControl of days.controls; let i = index"
            class="day-card mb-3"
            [formGroupName]="i">

            <div class="day-header">
              <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                  <span class="day-number">{{i + 1}}. Gün</span>
                </h6>
              </div>
            </div>

            <div class="day-body">
              <div class="row g-3">
                <!-- Day Name -->
                <div class="col-md-6">
                  <div class="modern-form-group">
                    <label class="modern-form-label">
                      Gün Adı <span class="text-danger">*</span>
                    </label>
                    <input
                      type="text"
                      class="modern-form-control"
                      [class.is-invalid]="isDayFieldInvalid(i, 'dayName')"
                      formControlName="dayName"
                      placeholder="Örn: Göğüs-Triceps">
                    <div *ngIf="isDayFieldInvalid(i, 'dayName')" class="invalid-feedback">
                      {{getDayFieldError(i, 'dayName')}}
                    </div>
                  </div>
                </div>

                <!-- Popular Day Names -->
                <div class="col-md-4">
                  <div class="modern-form-group">
                    <label class="modern-form-label">Popüler Gün Adları</label>
                    <select
                      class="modern-form-control"
                      (change)="onPopularDayNameSelect(i, $any($event.target).value)">
                      <option value="">Seçiniz</option>
                      <option *ngFor="let dayName of popularDayNames" [value]="dayName">
                        {{dayName}}
                      </option>
                    </select>
                  </div>
                </div>

                <!-- Rest Day -->
                <div class="col-md-2">
                  <div class="modern-form-group">
                    <label class="modern-form-label">Dinlenme Günü</label>
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        formControlName="isRestDay"
                        (change)="onRestDayChange(i)"
                        [id]="'restDay' + i">
                      <label class="form-check-label" [for]="'restDay' + i">
                        {{dayControl.get('isRestDay')?.value ? 'Evet' : 'Hayır'}}
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="dayControl.get('isRestDay')?.value" class="mt-3">
                <div class="alert alert-warning">
                  <small>
                    <fa-icon [icon]="faInfoCircle" class="me-1"></fa-icon>
                    Bu gün dinlenme günü olarak işaretlenmiştir
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Step 3: Exercise Setup -->
  <div *ngIf="currentStep === WizardStep.EXERCISE_SETUP" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faDumbbell" class="me-2"></fa-icon>
        Egzersiz Kurulumu
      </h5>
      <p class="mb-0 mt-2 text-muted">
        <small>Egzersiz günleri için egzersizleri ekleyin ve düzenleyin</small>
      </p>
    </div>
    <div class="modern-card-body">
      <div class="row g-3">
        <div
          *ngFor="let dayControl of days.controls; let i = index"
          class="col-md-6 col-lg-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h6 class="mb-0">{{i + 1}}. Gün - {{dayControl.get('dayName')?.value}}</h6>
            </div>
            <div class="modern-card-body text-center">
              <div *ngIf="dayControl.get('isRestDay')?.value" class="text-muted">
                <fa-icon [icon]="faInfoCircle" class="mb-2" style="font-size: 2rem;"></fa-icon>
                <p class="mb-0">Dinlenme Günü</p>
              </div>
              <div *ngIf="!dayControl.get('isRestDay')?.value">
                <div class="mb-3">
                  <fa-icon
                    [icon]="faDumbbell"
                    class="mb-2"
                    style="font-size: 2rem;"
                    [class.text-success]="isDayConfigured(i)"
                    [class.text-muted]="!isDayConfigured(i)">
                  </fa-icon>
                  <p class="mb-0">
                    <span *ngIf="getDayExerciseCount(i) === 0" class="text-muted">Egzersiz eklenmedi</span>
                    <span *ngIf="getDayExerciseCount(i) > 0" class="text-success">{{getDayExerciseCount(i)}} egzersiz</span>
                  </p>
                </div>
                <button
                  type="button"
                  class="modern-btn modern-btn-primary modern-btn-sm"
                  (click)="openDayEditModal(i)">
                  <fa-icon [icon]="faEdit" class="me-1"></fa-icon>
                  {{getDayExerciseCount(i) === 0 ? 'Egzersiz Ekle' : 'Düzenle'}}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Step 4: Preview -->
  <div *ngIf="currentStep === WizardStep.PREVIEW" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faEye" class="me-2"></fa-icon>
        Program Önizlemesi
      </h5>
      <p class="mb-0 mt-2 text-muted">
        <small>Programınızı gözden geçirin ve kaydedin</small>
      </p>
    </div>
    <div class="modern-card-body">
      <!-- Program Summary -->
      <div class="row g-3 mb-4">
        <div class="col-md-6">
          <div class="modern-card">
            <div class="modern-card-body">
              <h6 class="fw-bold">Program Bilgileri</h6>
              <p class="mb-1"><strong>Program Adı:</strong> {{programData.programName}}</p>
              <p class="mb-1"><strong>Deneyim Seviyesi:</strong> {{programData.experienceLevel}}</p>
              <p class="mb-1"><strong>Hedef:</strong> {{programData.targetGoal}}</p>
              <p class="mb-0"><strong>Açıklama:</strong> {{programData.description || 'Belirtilmemiş'}}</p>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="modern-card">
            <div class="modern-card-body">
              <h6 class="fw-bold">Program İstatistikleri</h6>
              <p class="mb-1"><strong>Toplam Gün:</strong> {{selectedDayCount}} gün</p>
              <p class="mb-1"><strong>Antrenman Günü:</strong> {{getTrainingDayCount()}} gün</p>
              <p class="mb-1"><strong>Dinlenme Günü:</strong> {{getRestDayCount()}} gün</p>
              <p class="mb-0"><strong>Toplam Egzersiz:</strong> {{getTotalExerciseCount()}} egzersiz</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Days Preview -->
      <div class="row g-3">
        <div
          *ngFor="let dayControl of days.controls; let i = index"
          class="col-md-6 col-lg-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h6 class="mb-0">{{i + 1}}. Gün - {{dayControl.get('dayName')?.value}}</h6>
            </div>
            <div class="modern-card-body">
              <div *ngIf="dayControl.get('isRestDay')?.value" class="text-center text-muted">
                <fa-icon [icon]="faInfoCircle" class="mb-2"></fa-icon>
                <p class="mb-0">Dinlenme Günü</p>
              </div>
              <div *ngIf="!dayControl.get('isRestDay')?.value">
                <div *ngIf="getDayExerciseCount(i) === 0" class="text-center text-warning">
                  <fa-icon [icon]="faInfoCircle" class="mb-2"></fa-icon>
                  <p class="mb-0">Egzersiz eklenmedi</p>
                </div>
                <div *ngIf="getDayExerciseCount(i) > 0">
                  <p class="text-success mb-2">
                    <fa-icon [icon]="faCheck" class="me-1"></fa-icon>
                    {{getDayExerciseCount(i)}} egzersiz eklendi
                  </p>
                  <ul class="list-unstyled mb-0">
                    <li
                      *ngFor="let exercise of dayControl.get('exercises')?.value; let j = index"
                      class="small text-muted">
                      {{j + 1}}. {{exercise.exerciseName}}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
